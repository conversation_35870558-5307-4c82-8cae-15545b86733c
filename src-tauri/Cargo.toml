[package]
name = "trading-desktop"
version = "0.1.0"
edition = "2021"
license = "GPL-3.0-or-later"

[build-dependencies]
tauri-build = { version = "2.0.0-beta.9", features = [] }

[dependencies]
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }
log = "0.4"
serde = { version = "1.0", features = ["derive"] }
tauri = { version = "2.0.0-beta.9", features = [] }
tauri-plugin-shell = "2.0.0-beta.2"
trading-core = { path = "../trading-core" }
tokio = { version = "1.0", features = ["full"] }
tracing = "0.1"
tracing-subscriber = "0.3"
rust_decimal = { version = "1.32", features = ["serde"] }

[features]
custom-protocol = ["tauri/custom-protocol"]
