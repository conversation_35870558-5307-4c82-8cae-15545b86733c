{"$schema": "https://schema.tauri.app/config/2", "productName": "rust-trade", "version": "0.1.0", "identifier": "com.rust-trade.dev", "build": {"beforeDevCommand": "cd ../frontend && npm run dev", "beforeBuildCommand": "cd ../frontend && npm run build", "devUrl": "http://localhost:3000", "frontendDist": "../frontend/out"}, "app": {"windows": [{"title": "Rust Trade", "width": 1200, "height": 800, "resizable": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}