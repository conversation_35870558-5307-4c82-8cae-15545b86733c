const nextConfig = {
  // Remove static export configuration for web deployment
  images: {
    unoptimized: false, // Enable Next.js image optimization
  },
  // Remove Tauri-specific configurations
  webpack: (config: { resolve: { fallback: any; }; }) => {
    // Keep webpack fallbacks for browser compatibility
    config.resolve.fallback = {
      ...(config.resolve.fallback || {}),
      fs: false,
      path: false,
    };
    return config;
  },
};

module.exports = nextConfig;
