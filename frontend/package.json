{"name": "frontend", "version": "0.1.0", "license": "GPL-3.0-or-later", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "preview": "next build && next start"}, "dependencies": {"@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-slot": "^1.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "lightweight-charts": "^4.2.2", "lucide-react": "^0.469.0", "next": "15.1.2", "react": "^19.0.0", "react-day-picker": "^9.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}