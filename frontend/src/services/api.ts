// API service layer to replace <PERSON><PERSON> invoke calls
export interface BacktestConfig {
  start_time: string;
  end_time: string;
  initial_capital: string;
  symbol: string;
  commission_rate: string;
}

export interface BacktestRequest {
  strategy_type: string;
  config: BacktestConfig;
  parameters: Record<string, string>;
}

export interface BacktestResponse {
  equity_curve: Array<{ timestamp: string; value: string }>;
  losing_trades: number;
  max_drawdown: string;
  total_return: string;
  total_trades: number;
  trades: Array<{
    timestamp: string;
    side: 'Buy' | 'Sell';
    symbol: string;
    quantity: string;
    price: string;
    commission: string;
  }>;
  winning_trades: number;
}

class ApiService {
  private baseUrl: string;

  constructor() {
    // Use environment variable or default to localhost
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api';
  }

  async runBacktest(request: BacktestRequest): Promise<BacktestResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/backtest`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API call failed:', error);
      throw new Error('Failed to connect to backend API');
    }
  }

  // Add more API methods as needed
  async getMarketData(symbol: string, timeframe: string) {
    // Implementation for market data API
  }

  async getStrategies() {
    // Implementation for strategies API
  }
}

export const apiService = new ApiService();