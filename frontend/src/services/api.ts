// API service layer to replace <PERSON><PERSON> invoke calls
export interface BacktestConfig {
  start_time: string;
  end_time: string;
  initial_capital: string;
  symbol: string;
  commission_rate: string;
}

export interface BacktestRequest {
  strategy_type: string;
  config: BacktestConfig;
  parameters: Record<string, string>;
}

export interface BacktestResponse {
  equity_curve: Array<{ timestamp: string; value: string }>;
  losing_trades: number;
  max_drawdown: string;
  total_return: string;
  total_trades: number;
  trades: Array<{
    timestamp: string;
    side: 'Buy' | 'Sell';
    symbol: string;
    quantity: string;
    price: string;
    commission: string;
  }>;
  winning_trades: number;
}

class ApiService {
  private baseUrl: string;
  private useMockData: boolean;

  constructor() {
    // Use environment variable or default to localhost
    this.baseUrl = (typeof window !== 'undefined' ? '' : process.env.NEXT_PUBLIC_API_URL) || 'http://localhost:8080/api';
    // Enable mock data when no backend is available
    this.useMockData = (typeof window !== 'undefined' ? true : process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true') || !this.baseUrl.includes('localhost');
  }

  async runBacktest(request: BacktestRequest): Promise<BacktestResponse> {
    if (this.useMockData) {
      return this.mockBacktest(request);
    }

    try {
      const response = await fetch(`${this.baseUrl}/backtest`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API call failed, falling back to mock data:', error);
      return this.mockBacktest(request);
    }
  }

  private async mockBacktest(request: BacktestRequest): Promise<BacktestResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Generate realistic mock data
    const days = Math.floor((new Date(request.config.end_time).getTime() - new Date(request.config.start_time).getTime()) / (1000 * 60 * 60 * 24));
    const initialCapital = parseFloat(request.config.initial_capital);

    // Generate equity curve
    const equityCurve = [];
    let currentValue = initialCapital;
    const startDate = new Date(request.config.start_time);

    for (let i = 0; i <= days; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);

      // Simulate market movements with some volatility
      const dailyReturn = (Math.random() - 0.48) * 0.02; // Slight positive bias
      currentValue *= (1 + dailyReturn);

      equityCurve.push({
        timestamp: date.toISOString(),
        value: currentValue.toFixed(2)
      });
    }

    // Generate mock trades
    const trades = [];
    const numTrades = Math.floor(days / 3); // Trade every 3 days on average
    let winningTrades = 0;

    for (let i = 0; i < numTrades; i++) {
      const tradeDate = new Date(startDate);
      tradeDate.setDate(tradeDate.getDate() + Math.floor(Math.random() * days));

      const side: 'Buy' | 'Sell' = Math.random() > 0.5 ? 'Buy' : 'Sell';
      const price = (45000 + Math.random() * 10000).toFixed(2);
      const quantity = (Math.random() * 0.5 + 0.1).toFixed(3);
      const commission = (parseFloat(price) * parseFloat(quantity) * parseFloat(request.config.commission_rate)).toFixed(2);

      // Simulate win/loss
      if (Math.random() > 0.4) winningTrades++; // 60% win rate

      trades.push({
        timestamp: tradeDate.toISOString(),
        side,
        symbol: request.config.symbol,
        quantity,
        price,
        commission
      });
    }

    const totalReturn = ((currentValue - initialCapital) / initialCapital * 100).toFixed(2);
    const maxDrawdown = (Math.random() * 15 + 5).toFixed(2); // 5-20% drawdown

    return {
      equity_curve: equityCurve,
      losing_trades: numTrades - winningTrades,
      max_drawdown: maxDrawdown,
      total_return: totalReturn,
      total_trades: numTrades,
      trades,
      winning_trades: winningTrades
    };
  }

  async getMarketData(symbol: string, timeframe: string) {
    if (this.useMockData) {
      // Return mock market data
      return {
        symbol,
        timeframe,
        data: Array(100).fill(0).map((_, i) => ({
          timestamp: new Date(Date.now() - (100 - i) * 24 * 60 * 60 * 1000).toISOString(),
          open: 45000 + Math.random() * 1000,
          high: 45000 + Math.random() * 1500,
          low: 45000 - Math.random() * 1000,
          close: 45000 + Math.random() * 1000,
          volume: Math.random() * 1000000
        }))
      };
    }

    try {
      const response = await fetch(`${this.baseUrl}/market-data/${symbol}?timeframe=${timeframe}`);
      if (!response.ok) throw new Error('Failed to fetch market data');
      return await response.json();
    } catch (error) {
      console.error('Market data API failed:', error);
      throw error;
    }
  }

  async getStrategies() {
    if (this.useMockData) {
      return [
        { id: 'SMACross', name: 'Simple Moving Average Cross', description: 'Classic trend-following strategy' },
        { id: 'RSI', name: 'RSI Oversold/Overbought', description: 'Mean reversion strategy using RSI' },
        { id: 'MACD', name: 'MACD Signal', description: 'Momentum strategy using MACD crossovers' }
      ];
    }

    try {
      const response = await fetch(`${this.baseUrl}/strategies`);
      if (!response.ok) throw new Error('Failed to fetch strategies');
      return await response.json();
    } catch (error) {
      console.error('Strategies API failed:', error);
      throw error;
    }
  }
}

export const apiService = new ApiService();