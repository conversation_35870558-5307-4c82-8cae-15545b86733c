/** @type {import('next').NextConfig} */
const nextConfig = {
  // Standard web deployment configuration
  images: {
    unoptimized: false, // Enable Next.js image optimization for web
  },
  // Standard webpack configuration for web deployment
  webpack: (config: any) => {
    // Browser compatibility fallbacks
    config.resolve.fallback = {
      ...(config.resolve.fallback || {}),
      fs: false,
      path: false,
    };
    return config;
  },
  // Enable standalone output for better deployment options
  output: 'standalone',
};

module.exports = nextConfig;
