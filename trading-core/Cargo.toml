[package]
name = "trading-core"
version = "0.1.0"
edition = "2021"
license = "GPL-3.0-or-later"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
tokio-tungstenite = { version = "0.20", features = ["native-tls"] }
futures-util = "0.3"
reqwest = { version = "0.11", features = ["json", "blocking"] }
sqlx = { version = "0.7", features = ["runtime-tokio", "tls-rustls", "postgres", "chrono", "bigdecimal"] }
bigdecimal = { version = "0.4", features = ["serde"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
config = "0.13"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
dotenv = "0.15"
chrono = { version = "0.4.35", features = ["serde"] }
uuid = { version = "1.0", features = ["serde", "v4"] }
anyhow = "1.0"
thiserror = "1.0"
rust_decimal = { version = "1.32", features = ["serde"] }
async-trait = "0.1"
clap = { version = "4.4", features = ["derive"] }


subxt = "0.32.1"
subxt-signer = { version = "0.32.1", features = ["subxt"] }
codec = { package = "parity-scale-codec", version = "3.6.5", features = ["derive"] }
sp-core = "21.0.0"
sp-keyring = "24.0.0"

[dev-dependencies]
criterion = "0.5"

[[bench]]
name = "market_data_cache"
harness = false