# Rust Trading System (rust-trade)
A quantitative trading system with a web-based frontend and Rust backend

[![My Skills](https://skillicons.dev/icons?i=rust,nextjs,ts,react,postgresql)](https://skillicons.dev)

This system provides a modern web-based interface for quantitative trading operations, built with Next.js frontend and a powerful Rust backend for high-performance trading operations.

## Overview

```bash
rust-trade/
├── frontend/                    # Next.js web application
│   ├── src/
│   │   ├── app/                # Next.js app router pages
│   │   ├── components/         # React components
│   │   └── services/           # API service layer
│   └── package.json            # Frontend dependencies
│
├── trading-core/                # Core trading library
│   ├── src/
│   │   ├── lib.rs              # Library entry point
│   │   ├── data/               # Data management module
│   │   │   ├── mod.rs          # Data module entry point
│   │   │   ├── cache.rs        # Market data caching system
│   │   │   ├── database.rs     # Database operations
│   │   │   └── market_data.rs  # Market data management
│   │   │
│   │   ├── backtest/           # Backtesting system
│   │   │   ├── engine.rs       # Backtesting engine
│   │   │   ├── metrics.rs      # Performance metrics calculation
│   │   │   └── types.rs        # Backtesting type definitions
│   │   │
│   │   └── blockchain/         # Blockchain module
│   │       ├── mod.rs          # Blockchain manager
│   │       ├── error.rs        # Error definitions
│   │       └── types.rs        # Type definitions
│   │
│   ├── benches/                # Performance benchmarks
│   │   └── market_data_cache.rs
│   └── Cargo.toml              # Core library dependencies
│
├── substrate-test-node/         # Local test node
│   ├── Dockerfile              # Node container definition
│   └── docker-compose.yml      # Container orchestration configuration
│
└── frontend/                    # Next.js frontend
    └── src/
        ├── app/
        │   └── page.tsx        # Main page
        └── components/         # Shared components

```

## How to run

### Frontend (Web Application)

Navigate to the frontend directory and run:

```bash
cd frontend

# Install dependencies
npm install

# Start the development server
npm run dev
```

The web application will be available at http://localhost:3000

```bash
# Build for production
npm run build

# Start production server
npm run start
```

### Backend (Trading Core)

The trading core can be run independently:

```bash
# Run the trading core
cargo run --bin trading-core
```

## Example

![result](assets/version2NFT.png)

![result](assets/version2Strategy.png)

## Required Environment Variables
```bash
DATABASE_URL=postgresql://user:password@localhost/dbname
```

## Development Roadmap

1. Add more strategy templates
2. Implement strategy scoring system
3. Develop strategy market function
4. Add real trading support
5. Optimize performance indicator calculation
6. Add more data analysis tools

## License
This program is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program. If not, see <https://www.gnu.org/licenses/>.
